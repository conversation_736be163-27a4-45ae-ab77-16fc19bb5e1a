#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QButtonGroup>
#include <QKeyEvent>
#include <QLabel>
#include <QTimer>
#include <QHBoxLayout>
#include <QResizeEvent>
#include <QDateTime>
// 前向声明
class CameraStream;
class ch9350_mouse;
// 录像设置结构体
struct RecordingSettings {
    QString path;                   // 录像路径
    QString basePath;               // 基础路径
    QString name;                   // 录像前缀名
    bool segmentedVideo = false;    // 是否分段录像
    bool recordingByTime = false;   // 按时间分段
    bool byFileSize = false;        // 按文件大小分段
    int segmentedTime = 0;          // 分段时间（分钟）
    int segmentedSize = 0;          // 分段大小（MB）
    int recordingQuality = 0;       // 录像质量
    bool videoStorage = false;      // 存储限制
    double storageSize = 0.0;       // 存储大小限制
};

QT_BEGIN_NAMESPACE
namespace Ui {
class MainWindow;
}
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();
    int mouse_fd;


    // 音频状态，单通道
    bool curaudiostate = false;

    // 录像设置，单通道
    RecordingSettings recordingSettings;

    // 禁用相机控制（录像时调用）
    void disableCameraControls();

    // 重新启用相机控制（停止录像时调用）
    void enableCameraControls();

private:
    Ui::MainWindow *ui;
    QButtonGroup *mainGroup;        // 主菜单按钮组
    QList<QButtonGroup*> subGroups; // 子菜单按钮组列表
    bool isRecordEnabled;
    bool isAudioEnabled;
    bool isPhotoEnabled;

    // 拍照状态枚举
    enum PhotoState {
        PHOTO_READY,    // 是否拍照
        PHOTO_SUCCESS,  // 拍照成功
        PHOTO_PROCESSING // 拍照处理中
    };
    PhotoState currentPhotoState;
    CameraStream *cameraStream;

    ch9350_mouse *m_mouseThread;

    // 录像指示器相关
    QWidget *recordingIndicatorWidget;      // 录像指示器容器
    QLabel *recordingDotLabel;              // 红色圆点标签
    QLabel *recordingTimeLabel;             // 录像时间标签
    QTimer *blinkTimer;                     // 闪烁定时器
    QTimer *recordingTimer;                 // 录像时间计时器
    QDateTime recordingStartTime;           // 录像开始时间
    bool isDotVisible;                      // 圆点是否可见（用于闪烁效果）

    // UI initialization methods
    void initializeUI();            //UI基础设置
    void setupButtonGroups();       //按钮组配置
    void setupRecordingSubGroup();  //录像子菜单按钮组配置
    void initializeLabels();        //标签初始化
    void initializeCameraStream();  //摄像头流初始化

    // Event handling methods
    void keyPressEvent(QKeyEvent *e) override;  // 键盘事件处理
    void handleQKeyPress();                     // Q键处理
    void handleEnterKeyPress();                 // Enter键处理
    void handleLeftRightKeyPress();             // 左右键处理
    void handleUpDownKeyPress(int key);         // 上下键处理

    // Navigation methods
    void navigateToMainMenu();                  // 返回主菜单
    void toggleMainMenuVisibility();            // 切换主菜单显示/隐藏
    void navigateSubMenu(bool moveUp);          // 子菜单导航
    void navigateMainMenu(bool moveUp);         // 主菜单导航

    // State management methods
    void toggleRecordingState();                // 录像状态切换
    void toggleAudioState();                    // 音频状态切换
    void togglePhotoState();                    // 拍照状态切换
    void updateRecordLabel();                   // 更新录像标签显示
    void updateAudioLabel();                    // 更新音频标签显示
    void updatePhotoLabel();                    // 更新拍照标签显示
    void handleRecordingStateChange();          // 处理录像状态改变

    // Utility methods
    bool validateCameraStream() const;          // 摄像头流验证
    bool isValidChannel(int channel) const;     // 通道有效性检查
    void startRecording();                      // 开始录像
    void stopRecording();                       // 停止录像

    // 录像指示器相关方法
    void setupRecordingIndicator();             // 设置录像指示器
    void showRecordingIndicator();              // 显示录像指示器
    void hideRecordingIndicator();              // 隐藏录像指示器
    void updateRecordingTime();                 // 更新录像时间显示
    void toggleRecordingDot();                  // 切换红点显示状态（闪烁效果）
    void positionRecordingIndicator();          // 定位录像指示器到右上角

private slots:
    void onPhotoTaken();                        // 拍照成功处理槽函数
    void onCameraDisconnected();                // 摄像头断开处理槽函数
    void onCameraReconnected();                 // 摄像头重连处理槽函数

protected:
    void resizeEvent(QResizeEvent *event) override;  // 窗口大小改变事件
};
#endif // MAINWINDOW_H
